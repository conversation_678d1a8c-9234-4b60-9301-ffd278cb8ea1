import 'package:dio/dio.dart';
import 'package:gather_point/core/utils/app_constants.dart';
import 'package:gather_point/feature/auth/Data/Data%20Sources/login_local_data_source.dart';
import 'package:gather_point/feature/auth/Data/Data%20Sources/login_remote_data_source.dart';
import 'package:gather_point/feature/auth/Data/Repos/login_repo_impl.dart';
import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';
import 'package:gather_point/feature/auth/Domain/Use%20Cases/login_guest_use_case.dart';
import 'package:gather_point/feature/auth/Domain/Use%20Cases/login_token_use_case.dart';
import 'package:gather_point/feature/auth/Domain/Use%20Cases/login_use_case.dart';
import 'package:gather_point/feature/auth/Domain/Use%20Cases/validate_otp_use_case.dart';
import 'package:gather_point/feature/auth/Domain/Use%20Cases/validate_token_use_case.dart';
import 'package:gather_point/feature/host/data/services/host_api_service.dart';
import 'package:gather_point/feature/host/data/services/withdrawal_api_service.dart';
import 'package:gather_point/feature/reservations/data/services/reservations_api_service.dart';
import 'package:gather_point/feature/dashboard/data/services/properties_api_service.dart';
import 'package:gather_point/feature/reviews/data/services/reviews_api_service.dart';
import 'package:get_it/get_it.dart';
import 'package:hive/hive.dart';
import '../databases/api/dio_consumer.dart';

final getIt = GetIt.instance;

void setupServiceLocator() {
  // Register Hive box first
  getIt.registerLazySingleton<Box<UserEntity>>(
    () => Hive.box<UserEntity>(AppConstants.kMyProfileBoxName),
  );
  getIt.registerSingleton<Box<bool>>(
      Hive.box<bool>(AppConstants.kSettingsBoxName));

  // Register Dio instance
  getIt.registerSingleton<Dio>(Dio());

  // Register DioConsumer with dependencies
  getIt.registerSingleton<DioConsumer>(
    DioConsumer(
      dio: getIt<Dio>(),
      profileBox: getIt<Box<UserEntity>>(), // Inject the Hive box
    ),
  );

  //*** FCM service ***/
  // FCM service doesn't need to be registered as it uses static methods

  //*** login service ***/
  getIt.registerSingleton<LoginRemoteDataSourceImpl>(
    LoginRemoteDataSourceImpl(
      apiConsumer: getIt<DioConsumer>(),
    ),
  );

  // Rest of your registrations remain the same...
  getIt.registerSingleton<LoginLocalDataSourceImpl>(LoginLocalDataSourceImpl());

  getIt.registerSingleton<LoginRepoImpl>(
    LoginRepoImpl(
      loginLocalDataSource: getIt<LoginLocalDataSourceImpl>(),
      loginRemoteDataSource: getIt<LoginRemoteDataSourceImpl>(),
    ),
  );

  //*** Login Use Cases ***/
  getIt.registerSingleton<LoginUseCase>(
    LoginUseCase(loginRepo: getIt<LoginRepoImpl>()),
  );

  getIt.registerSingleton<LoginGuestUseCase>(
    LoginGuestUseCase(loginRepo: getIt<LoginRepoImpl>()),
  );

  getIt.registerSingleton<ValidateOTPUseCase>(
    ValidateOTPUseCase(loginRepo: getIt<LoginRepoImpl>()),
  );

  getIt.registerSingleton<LoginTokenUseCase>(
    LoginTokenUseCase(loginRepo: getIt<LoginRepoImpl>()),
  );

  getIt.registerSingleton<ValidateTokenUseCase>(
    ValidateTokenUseCase(loginRepo: getIt<LoginRepoImpl>()),
  );

  //*** API Services ***/
  getIt.registerSingleton<HostApiService>(
    HostApiService(getIt<DioConsumer>()),
  );

  getIt.registerSingleton<WithdrawalApiService>(
    WithdrawalApiService(getIt<DioConsumer>()),
  );

  getIt.registerSingleton<ReservationsApiService>(
    ReservationsApiService(getIt<DioConsumer>()),
  );

  getIt.registerSingleton<PropertiesApiService>(
    PropertiesApiService(getIt<DioConsumer>()),
  );

  getIt.registerSingleton<ReviewsApiService>(
    ReviewsApiService(getIt<DioConsumer>()),
  );
}
