import 'package:dio/dio.dart';
import 'package:gather_point/core/databases/api/end_points.dart';
import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/core/utils/app_constants.dart';
import 'package:gather_point/feature/auth/Data/Models/user_model/user_model.dart';
import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';
import 'package:hive/hive.dart';

abstract class LoginRemoteDataSource {
  Future<void> login({required String phone});

  Future<UserEntity> validateOTP({required String phone, required String otp});

  Future<UserEntity> loginToken({required String accessToken});

  Future<UserEntity> loginGuest();

  Future<UserEntity> validateToken({required String token});
}

class LoginRemoteDataSourceImpl implements LoginRemoteDataSource {
  final DioConsumer apiConsumer;

  LoginRemoteDataSourceImpl({required this.apiConsumer});

  @override
  Future<void> login({required String phone}) async {
    await apiConsumer.post(
      EndPoints.login,
      data: {'phone': phone},
    );
  }

  @override
  Future<UserEntity> validateOTP(
      {required String phone, required String otp}) async {
    var data = await apiConsumer.post(
      EndPoints.validateOTP,
      data: {'phone': phone, 'otp': otp},
    );

    UserEntity user = UserModel.fromJson(data['data']);

    return user;
  }

  @override
  Future<UserEntity> loginToken({required String accessToken}) async {
    var data = await apiConsumer.post(
      EndPoints.loginToken,
      data: {
        'login_token': accessToken,
        'login_method': '2',
      },
    );

    UserEntity user = UserModel.fromJson(data['data']);

    return user;
  }

  @override
  Future<UserEntity> loginGuest() async {
    var data = await apiConsumer.post(
      EndPoints.loginGuest,
    );

    UserEntity user = UserModel.fromJson(data['data']);

    return user;
  }

  @override
  Future<UserEntity> validateToken({required String token}) async {
    // Create a temporary Dio instance with the specific token for validation
    final tempDio = Dio();
    tempDio.options.baseUrl = EndPoints.baserUrl;
    tempDio.options.headers['Accept'] = 'application/json';
    tempDio.options.headers['Content-Type'] = 'application/json';
    tempDio.options.headers['Authorization'] = 'Bearer $token';

    await tempDio.post(EndPoints.validateToken);

    // If we reach here, the token is valid
    // The validate endpoint might not return user data, just validation status
    // So we need to return the user with the validated token
    // Since we don't have full user data from validate endpoint,
    // we'll need to get it from storage and update the token
    final profileBox = Hive.box<UserEntity>(AppConstants.kMyProfileBoxName);
    final existingUser = profileBox.get(AppConstants.kMyProfileKey);

    if (existingUser != null) {
      // Return the existing user with the validated token
      return existingUser.copyWith(token: token);
    } else {
      // This shouldn't happen, but if it does, we need to handle it
      throw Exception('User data not found in storage');
    }
  }
}
