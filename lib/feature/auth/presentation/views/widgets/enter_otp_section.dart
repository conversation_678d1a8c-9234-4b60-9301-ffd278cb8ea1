import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gather_point/core/styles/app_colors.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/feature/auth/presentation/Manager/validate_otp_cubit/validate_otp_cubit.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:pinput/pinput.dart';
import 'package:sms_autofill/sms_autofill.dart';

class EnterOTPSection extends StatefulWidget {
  const EnterOTPSection({
    super.key,
    required this.formKey,
    this.onOtpCompleted,
  });

  final GlobalKey<FormState> formKey;
  final VoidCallback? onOtpCompleted;

  @override
  State<EnterOTPSection> createState() => _EnterOTPSectionState();
}

class _EnterOTPSectionState extends State<EnterOTPSection> {
  late TextEditingController _pinController;
  String _otpCode = '';

  @override
  void initState() {
    super.initState();
    _pinController = TextEditingController();
    // Start listening for SMS
    _listenForSms();
  }

  void _listenForSms() async {
    try {
      await SmsAutoFill().listenForCode();
    } catch (e) {
      // Handle error silently - SMS autofill is optional
      debugPrint('SMS AutoFill error: $e');
    }
  }

  @override
  void dispose() {
    _pinController.dispose();
    try {
      SmsAutoFill().unregisterListener();
    } catch (e) {
      // Handle error silently
      debugPrint('SMS AutoFill dispose error: $e');
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    // Theme-aware pin theme
    PinTheme pinTheme = PinTheme(
      width: 66,
      height: 57,
      textStyle: AppTextStyles.font30SemiBold.copyWith(
        color: isDark ? AppColors.white : AppColors.black,
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: isDark ? AppColors.darkGrey : AppColors.white,
        border: Border.all(
          color: isDark ? AppColors.darkGrey8 : AppColors.lightGrey,
          width: 1,
        ),
      ),
    );

    PinTheme focusedPinTheme = pinTheme.copyWith(
      decoration: pinTheme.decoration!.copyWith(
        border: Border.all(
          color: AppColors.yellow,
          width: 2,
        ),
      ),
    );

    return Form(
      key: widget.formKey,
      child: Center(
        child: Directionality(
          textDirection: TextDirection.ltr,
          child: PinFieldAutoFill(
            codeLength: 4,
            decoration: UnderlineDecoration(
              textStyle: AppTextStyles.font30SemiBold.copyWith(
                color: isDark ? AppColors.white : AppColors.black,
              ),
              colorBuilder: FixedColorBuilder(
                isDark ? AppColors.darkGrey8 : AppColors.lightGrey,
              ),
              bgColorBuilder: FixedColorBuilder(
                isDark ? AppColors.darkGrey : AppColors.white,
              ),
            ),
            currentCode: _otpCode,
            onCodeSubmitted: (code) {
              setState(() {
                _otpCode = code;
              });
              BlocProvider.of<ValidateOtpCubit>(context).otpCode = code;
              widget.onOtpCompleted?.call();
            },
            onCodeChanged: (code) {
              setState(() {
                _otpCode = code ?? '';
              });
              if (code != null) {
                BlocProvider.of<ValidateOtpCubit>(context).otpCode = code;
                if (code.length == 4) {
                  widget.onOtpCompleted?.call();
                }
              }
            },
          ),
        ),
      ),
    );
  }
}
