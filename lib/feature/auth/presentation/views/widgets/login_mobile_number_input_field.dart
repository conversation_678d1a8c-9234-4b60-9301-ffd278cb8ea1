import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gather_point/core/components/primary_button.dart';
import 'package:gather_point/core/styles/app_colors.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/utils/app_assets.dart';
import 'package:gather_point/core/utils/sound_manager.dart';
import 'package:gather_point/core/widgets/custom_error_toast.dart';
import 'package:gather_point/feature/auth/presentation/Manager/login_cubit/login_cubit.dart';
import 'package:gather_point/generated/l10n.dart';

class LoginMobileNumberInputField extends StatefulWidget {
  const LoginMobileNumberInputField({
    super.key,
  });

  @override
  State<LoginMobileNumberInputField> createState() =>
      _LoginMobileNumberInputFieldState();
}

class _LoginMobileNumberInputFieldState
    extends State<LoginMobileNumberInputField> {

  @override
  Widget build(BuildContext context) {
    final loginCubit = BlocProvider.of<LoginCubit>(context);
    final s = S.of(context);

    return Container(
      decoration: BoxDecoration(
        color: AppColors.black,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: AppColors.yellow),
      ),
      child: IntrinsicHeight(
        child: Directionality(
          textDirection: TextDirection.ltr,
          child: Row(
            children: [
              const SizedBox(width: 12),
              SvgPicture.asset(
                AppAssets.imagesSaudiArabia,
                width: 24,
                height: 24,
              ),
              const SizedBox(width: 8),
              Text(
                '+966',
                style: AppTextStyles.font16Bold.copyWith(
                  color: AppColors.white.withValues(alpha: 0.8),
                ),
              ),
              const SizedBox(width: 12),
              Container(
                width: 1,
                height: 24,
                color: AppColors.lightGrey8,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: TextField(
                  controller: loginCubit.phoneController,
                  textAlign: TextAlign.left,
                  textDirection: TextDirection.ltr,
                  style: AppTextStyles.font16Bold.copyWith(
                    color: AppColors.white,
                  ),
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                    LengthLimitingTextInputFormatter(9), // Saudi numbers are 9 digits after +966
                  ],
                  cursorColor: AppColors.yellow,
                  keyboardType: TextInputType.phone,
                  decoration: InputDecoration(
                    hintText: '5XXXXXXXX',
                    hintStyle: AppTextStyles.font16Bold.copyWith(
                      color: AppColors.white.withValues(alpha: 0.5),
                    ),
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
              ),
            const SizedBox(width: 8),
            PrimaryButton(
              fullWidth: false,
              onPressed: () {
                SoundManager.playClickSound();
                final phoneText = loginCubit.phoneController.text.trim();

                if (phoneText.isEmpty) {
                  showCustomErrorToast(s.pleaseEnterPhoneNumber);
                  return;
                } else if (!RegExp(
                        r'^(009665|9665|\+9665|05|5)(5|0|3|6|4|9|1|8|7)([0-9]{7})$')
                    .hasMatch(phoneText)) {
                  showCustomErrorToast(s.pleaseCheckPhoneNumber);
                  return;
                } else {
                  loginCubit.login();
                }
              },
              label: s.login,
              padding: const EdgeInsets.symmetric(
                horizontal: 24,
                vertical: 12,
              ),
              borderRadius: 20,
            ),
            const SizedBox(width: 12),
            ],
          ),
        ),
      ),
    );
  }
}
