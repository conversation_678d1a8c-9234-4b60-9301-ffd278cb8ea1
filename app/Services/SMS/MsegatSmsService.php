<?php

namespace App\Services\SMS;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;

class MsegatSmsService
{
    private $apiUrl;
    private $token;
    private $userName;
    private $userSender;

    public function __construct()
    {
        $this->apiUrl = 'https://www.msegat.com/gw/sendsms.php';
        $this->token = config('services.msegat.token', '07158616c9f86e5662ff28cb5d4f6d06');
        $this->userName = config('services.msegat.username', 'GatherPoint');
        $this->userSender = config('services.msegat.sender', 'GatherPoint');
    }

    /**
     * Send SMS message
     *
     * @param string $phoneNumber
     * @param string $message
     * @return array
     */
    public function sendSms($phoneNumber, $message)
    {
        try {
            // Format phone number for Saudi Arabia
            $formattedPhone = $this->formatPhoneNumber($phoneNumber);
            
            $data = [
                'apiKey' => $this->token,
                'userName' => $this->userName,
                'numbers' => $formattedPhone,
                'userSender' => $this->userSender,
                'msg' => $message,
                'msgEncoding' => 'UTF8'
            ];

            Log::info('Sending SMS via Msegat', [
                'phone' => $formattedPhone,
                'message' => $message
            ]);

            $response = Http::timeout(30)->post($this->apiUrl, $data);

            if ($response->successful()) {
                $responseData = $response->json();
                
                Log::info('Msegat SMS Response', ['response' => $responseData]);

                // Check if the response indicates success
                if (isset($responseData['code']) && $responseData['code'] == 'M0000') {
                    return [
                        'success' => true,
                        'message' => 'SMS sent successfully',
                        'response' => $responseData
                    ];
                } else {
                    return [
                        'success' => false,
                        'message' => $responseData['message'] ?? 'Failed to send SMS',
                        'response' => $responseData
                    ];
                }
            } else {
                Log::error('Msegat SMS API Error', [
                    'status' => $response->status(),
                    'body' => $response->body()
                ]);

                return [
                    'success' => false,
                    'message' => 'SMS gateway error: ' . $response->status(),
                    'response' => null
                ];
            }
        } catch (Exception $e) {
            Log::error('SMS Service Exception', [
                'error' => $e->getMessage(),
                'phone' => $phoneNumber
            ]);

            return [
                'success' => false,
                'message' => 'SMS service error: ' . $e->getMessage(),
                'response' => null
            ];
        }
    }

    /**
     * Send OTP SMS
     *
     * @param string $phoneNumber
     * @param string $otp
     * @param string $appName
     * @return array
     */
    public function sendOtp($phoneNumber, $otp, $appName = 'Gather Point')
    {
        $message = "كود التحقق الخاص بك في تطبيق {$appName} هو: {$otp}";
        
        return $this->sendSms($phoneNumber, $message);
    }

    /**
     * Format phone number for Saudi Arabia
     *
     * @param string $phoneNumber
     * @return string
     */
    private function formatPhoneNumber($phoneNumber)
    {
        // Remove any non-numeric characters
        $phone = preg_replace('/[^0-9]/', '', $phoneNumber);
        
        // Handle different Saudi phone number formats
        if (preg_match('/^(009665|9665|\+9665)/', $phone)) {
            // Already in international format, remove country code for local format
            $phone = substr($phone, -9);
        } elseif (preg_match('/^(05|5)/', $phone)) {
            // Local format starting with 05 or 5
            if (strlen($phone) == 10 && substr($phone, 0, 2) == '05') {
                $phone = substr($phone, 1); // Remove leading 0
            } elseif (strlen($phone) == 9 && substr($phone, 0, 1) == '5') {
                // Already in correct format
            } else {
                throw new Exception('Invalid phone number format');
            }
        } else {
            throw new Exception('Invalid Saudi phone number');
        }

        // Ensure the phone number is 9 digits starting with 5
        if (strlen($phone) != 9 || substr($phone, 0, 1) != '5') {
            throw new Exception('Invalid Saudi phone number format');
        }

        // Return in international format for SMS gateway
        return '966' . $phone;
    }

    /**
     * Validate Saudi phone number
     *
     * @param string $phoneNumber
     * @return bool
     */
    public function validatePhoneNumber($phoneNumber)
    {
        try {
            $this->formatPhoneNumber($phoneNumber);
            return true;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Get SMS delivery status
     *
     * @param string $messageId
     * @return array
     */
    public function getDeliveryStatus($messageId)
    {
        try {
            $data = [
                'apiKey' => $this->token,
                'userName' => $this->userName,
                'msgID' => $messageId
            ];

            $response = Http::timeout(30)->post('https://www.msegat.com/gw/msgstatus.php', $data);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'status' => $response->json()
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Failed to get delivery status'
                ];
            }
        } catch (Exception $e) {
            Log::error('SMS Status Check Exception', [
                'error' => $e->getMessage(),
                'messageId' => $messageId
            ]);

            return [
                'success' => false,
                'message' => 'Status check error: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get account balance
     *
     * @return array
     */
    public function getBalance()
    {
        try {
            $data = [
                'apiKey' => $this->token,
                'userName' => $this->userName
            ];

            $response = Http::timeout(30)->post('https://www.msegat.com/gw/credits.php', $data);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'balance' => $response->json()
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Failed to get balance'
                ];
            }
        } catch (Exception $e) {
            Log::error('SMS Balance Check Exception', [
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Balance check error: ' . $e->getMessage()
            ];
        }
    }
}
